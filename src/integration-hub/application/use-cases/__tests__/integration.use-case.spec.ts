import { Test, TestingModule } from '@nestjs/testing';
import { IntegrationUseCase } from '@integration-hub/application/use-cases/integration.use-case';
import { IntegrationStrategyManager } from '@integration-hub/domain/services/integration-strategy.manager';
import { IntegrationStrategyFactory } from '@integration-hub/domain/factories/integration-strategy.factory';
import { RecuperaIntegrationStrategy } from '@integration-hub/domain/strategies/recupera-integration.strategy';
import { PortfolioItemUpdatedRequestDTO } from '@integration-hub/application/dto/in/portfolio-item-updated-request.dto';
import { BusinessException } from '@common/exception/types/BusinessException';
import { CustomerChannelIntegrationDataDefinitionPort } from '@common/auth/db/ports/customer-channel-integration-data-definition.port';
import { HttpService } from '@nestjs/axios';

describe('IntegrationUseCase', () => {
  let useCase: IntegrationUseCase;
  let customerChannelIntegrationDataDefinitionPort: jest.Mocked<CustomerChannelIntegrationDataDefinitionPort>;
  let strategyManager: IntegrationStrategyManager;

  beforeEach(async () => {
    const mockCustomerChannelIntegrationDataDefinitionPort = {
      get: jest.fn(),
    };

    const mockHttpService = {
      post: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IntegrationUseCase,
        IntegrationStrategyManager,
        IntegrationStrategyFactory,
        RecuperaIntegrationStrategy,
        {
          provide: 'CustomerChannelIntegrationDataDefinitionPort',
          useValue: mockCustomerChannelIntegrationDataDefinitionPort,
        },
        {
          provide: 'BusinessBasePort',
          useValue: {
            sendDirectMessage: jest.fn(),
            getPortfolioItem: jest.fn(),
          },
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    useCase = module.get<IntegrationUseCase>(IntegrationUseCase);
    customerChannelIntegrationDataDefinitionPort = module.get('CustomerChannelIntegrationDataDefinitionPort');
    strategyManager = module.get<IntegrationStrategyManager>(IntegrationStrategyManager);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  it('should execute integration strategy successfully', async () => {
    const request: PortfolioItemUpdatedRequestDTO = {
      customerId: 'customer-id',
      portfolioItemId: 'portfolio-item-id',
      portfolioId: 'portfolio-id',
      workflowId: 'workflow-id',
      currentStatus: 'FINISHED',
      metadata: undefined,
    };

    const integrationConfig = {
      providerName: 'Recupera',
      credentials: {
        usuario: 'usuario',
        senha: 'senha',
        empresa: 'empresa',
        chaveAtivacao: 'chaveAtivacao',
      },
    };

    const customerChannelIntegrationDataDefinition = {
      id: 'customer-id',
      data: {
        integrationConfig,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    customerChannelIntegrationDataDefinitionPort.get.mockResolvedValue(customerChannelIntegrationDataDefinition);

    // Mock the strategy execution to avoid actual HTTP calls
    const mockResult = {
      success: true,
      strategyName: 'Recupera',
      message: 'Integration completed successfully',
      data: { portfolioItemId: 'portfolio-item-id' },
      executedAt: new Date(),
      hasData: () => true,
    };
    jest.spyOn(strategyManager, 'executeStrategy').mockResolvedValue(mockResult as any);

    const result = await useCase.executeIntegrationStrategy(request);

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
    expect(result.strategyName).toBe('Recupera');
    expect(customerChannelIntegrationDataDefinitionPort.get).toHaveBeenCalledWith('customer-id');
  });

  it('should throw exception when integration config is missing', async () => {
    const request: PortfolioItemUpdatedRequestDTO = {
      customerId: 'customer-id',
      portfolioItemId: 'portfolio-item-id',
      portfolioId: 'portfolio-id',
      workflowId: 'workflow-id',
      currentStatus: 'FINISHED',
      metadata: undefined,
    };

    const customerChannelIntegrationDataDefinition = {
      id: 'customer-id',
      data: {
        // No integration config
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    customerChannelIntegrationDataDefinitionPort.get.mockResolvedValue(customerChannelIntegrationDataDefinition);

    await expect(useCase.executeIntegrationStrategy(request)).rejects.toThrow(BusinessException);
  });

  it('should throw exception when customer channel integration data is missing', async () => {
    const request: PortfolioItemUpdatedRequestDTO = {
      customerId: 'customer-id',
      portfolioItemId: 'portfolio-item-id',
      portfolioId: 'portfolio-id',
      workflowId: 'workflow-id',
      currentStatus: 'FINISHED',
      metadata: undefined,
    };

    customerChannelIntegrationDataDefinitionPort.get.mockResolvedValue(null);

    await expect(useCase.executeIntegrationStrategy(request)).rejects.toThrow(BusinessException);
  });
});
